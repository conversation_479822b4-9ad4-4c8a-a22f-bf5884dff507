import { Injectable } from '@nestjs/common';
import * as PDFDocument from 'pdfkit';
import * as ExcelJS from 'exceljs';
import { LogAlarm } from '../../../database/entities/log-alarm.entity';
import * as dayjs from 'dayjs';
import axios from 'axios';
import { Branch } from '../../../database/entities/branch.entity';
import { Role } from '../../../database/entities/role.entity';
import { User } from '../../../database/entities/user.entity';
import { Label } from '../../../database/entities/label.entity';
import { Device } from '../../../database/entities/device.entity';
import { Timezone } from '../../../database/entities/timezone.entity';

export interface AlarmLogFiltersGenerateDocument {
  startDate: string | null;
  endDate: string | null;
  startTime: string | null;
  endTime: string | null;
  branch: Branch | null;
  role: Role | null;
  user: User | null;
  user_labels: Label[];
  device: Device | null;
  device_labels: Label[];
}

@Injectable()
export class AlarmLogGenerateDocumentService {
  constructor() {}

  /**
   * Generates a PDF document for a single alarm log entry
   * @param {LogAlarm} alarmLog - The alarm log data to generate PDF for
   * @param timezone
   * @returns {Promise<{buffer: Buffer, filename: string}>} PDF buffer and filename
   */
  async generatePDFById(
    alarmLog: LogAlarm,
    timezone: Timezone,
  ): Promise<{ buffer: Buffer; filename: string }> {
    // Create a new PDF document with custom options
    const doc = this.createPdfDoc();
    // Create a buffer to store the PDF
    const buffers: Buffer[] = [];
    doc.on('data', buffers.push.bind(buffers));

    /**
     * Helper function to draw section headers
     * @param {string} text - Header text
     * @param {number} y - Vertical position
     */
    const drawSectionHeader = (text: string, y: number) => {
      doc
        .fontSize(16)
        .font('Helvetica-Bold')
        .fillColor('#1a237e')
        .text(text, 50, y);

      doc
        .moveTo(50, y + 25)
        .lineTo(545, y + 25)
        .lineWidth(1)
        .strokeColor('#1a237e')
        .stroke();
    };

    /**
     * Adds a field row with label and value in the PDF document
     * @param {string} label - The field label
     * @param {string} value - The field value
     * @param {number} x - Horizontal position
     * @param {number} y - Vertical position
     * @param {number} [rowHeight=25] - Height of the row
     * @returns {number} New Y position after adding the row
     */
    const addFieldRow = (
      label: string,
      value: string,
      x: number,
      y: number,
      rowHeight: number = 25,
    ) => {
      const labelY = y + (rowHeight - doc.currentLineHeight()) / 2;
      const valueY = y + (rowHeight - doc.currentLineHeight()) / 2;

      doc
        .font('Helvetica-Bold')
        .fontSize(10)
        .fillColor('#546e7a')
        .text(label, x, labelY);

      doc
        .font('Helvetica')
        .fontSize(10)
        .fillColor('#263238')
        .text(value || '-', x + 90, valueY);

      return y + rowHeight;
    };

    // Add Page
    doc.addPage();

    /**
     * Adds company logo and name in the PDF header
     * Attempts to load logo from URL, falls back to simple rectangle if fails
     */
    try {
      const response = await axios.get(
        'https://trial-serverless.web.app/logo.png',
        {
          responseType: 'arraybuffer',
        },
      );
      const logoBuffer = Buffer.from(response.data, 'binary');

      // Then add the image
      doc.image(logoBuffer, 50, 50, {
        fit: [50, 50],
      });
    } catch (error) {
      console.error('Error loading logo:', error);
      // Fallback if logo loading fails
      doc.save().translate(75, 75).rect(-25, -25, 50, 50).fill('#1a237e');
    }

    // Add company name next to logo
    doc
      .font('Helvetica-Bold')
      .fontSize(20)
      .fillColor('#1a237e')
      .text('UNIGUARD', 110, 65);

    // Add generation time in header
    doc
      .font('Helvetica')
      .fontSize(10)
      .fillColor('#546e7a')
      .text(
        `Generated: ${dayjs(timezone.timezone_name).format('YYYY-MM-DD HH:mm:ss')}`,
        350,
        65,
      );

    // Add title and header
    drawSectionHeader('Activity Log Details', 120);

    // Add UUID right after header
    doc
      .font('Helvetica')
      .fontSize(10)
      .fillColor('#546e7a')
      .text(`UUID: ${alarmLog.uuid}`, 50, 155);

    // Add horizontal line after UUID
    doc
      .moveTo(50, 170)
      .lineTo(545, 170)
      .lineWidth(0.5)
      .strokeColor('#e0e0e0')
      .stroke();

    // Add entry details with improved layout
    let currentY = 190;

    // Create a table-like structure for the data
    const leftColumnData = [
      { label: 'Branch', value: alarmLog.branch_name },
      { label: 'Role', value: alarmLog.role_name },
      { label: 'User', value: alarmLog.user_name },
      { label: 'Device', value: alarmLog.device_name },
      { label: 'Timezone', value: alarmLog.timezone_name },
      {
        label: 'Original Time',
        value: dayjs(alarmLog.original_submitted_time).format(
          'YYYY-MM-DD HH:mm:ss',
        ),
      },
    ];

    const rightColumnData = [
      { label: 'Start Latitude', value: alarmLog.start_latitude?.toString() },
      { label: 'Start Longitude', value: alarmLog.start_longitude?.toString() },
      { label: 'End Latitude', value: alarmLog.end_latitude?.toString() },
      { label: 'End Longitude', value: alarmLog.end_longitude?.toString() },
      {
        label: 'Start Time',
        value: dayjs(alarmLog.start_date_time).format('YYYY-MM-DD HH:mm:ss'),
      },
      {
        label: 'End Time',
        value: dayjs(alarmLog.end_date_time).format('YYYY-MM-DD HH:mm:ss'),
      },
    ];

    // Draw table content with alternating background and vertical centering
    leftColumnData.forEach((item, i) => {
      const rowHeight = 30; // Increased row height for better spacing

      // Draw alternating background
      if (i % 2 === 0) {
        doc.fillColor('#fafafa').rect(50, currentY, 495, rowHeight).fill();
      }

      // Draw left column data
      currentY = addFieldRow(
        item.label + ':',
        item.value,
        50,
        currentY,
        rowHeight,
      );

      // Draw right column data if available
      if (i < rightColumnData.length) {
        addFieldRow(
          rightColumnData[i].label + ':',
          rightColumnData[i].value,
          300,
          currentY - rowHeight,
          rowHeight,
        );
      }
    });

    // Add footer
    const footerText = 'UniGuard Security System';
    const footerWidth = doc.widthOfString(footerText);
    doc
      .fontSize(10)
      .fillColor('#9e9e9e')
      .text(
        footerText,
        (doc.page.width - footerWidth) / 2,
        doc.page.height - 50,
      );

    // Finalize the PDF
    doc.end();

    return new Promise(resolve => {
      doc.on('end', () => {
        const buffer = Buffer.concat(buffers);
        const filename = `alarm_log_${alarmLog.id}_${dayjs().format('YYYYMMDD_HHmmss')}.pdf`;
        resolve({ buffer, filename });
      });
    });
  }

  /**
   * Generates a PDF document for multiple alarm log entries with filters
   * @param {LogAlarm[]} data - Array of alarm log data
   * @param {AlarmLogFiltersGenerateDocument} filters - Filters applied to the data
   * @param timezone
   * @returns {Promise<{buffer: Buffer, filename: string}>} PDF buffer and filename
   */
  async generatePDF(
    data: LogAlarm[],
    filters: AlarmLogFiltersGenerateDocument,
    timezone: Timezone,
  ): Promise<{ buffer: Buffer; filename: string }> {
    // Create a new PDF document with custom options
    const doc = this.createPdfDoc();
    // Create a buffer to store the PDF
    const buffers: Buffer[] = [];
    doc.on('data', buffers.push.bind(buffers));

    // Helper function to create section headers
    const drawSectionHeader = (text: string, y: number) => {
      doc
        .fontSize(16)
        .font('Helvetica-Bold')
        .fillColor('#1a237e')
        .text(text, 50, y);

      doc
        .moveTo(50, y + 25)
        .lineTo(545, y + 25)
        .lineWidth(1)
        .strokeColor('#1a237e')
        .stroke();
    };

    // Helper function to add field rows with vertical centering
    const addFieldRow = (
      label: string,
      value: string,
      x: number,
      y: number,
      rowHeight: number = 25,
    ) => {
      const labelY = y + (rowHeight - doc.currentLineHeight()) / 2;
      const valueY = y + (rowHeight - doc.currentLineHeight()) / 2;

      doc
        .font('Helvetica-Bold')
        .fontSize(10)
        .fillColor('#546e7a')
        .text(label, x, labelY);

      doc
        .font('Helvetica')
        .fontSize(10)
        .fillColor('#263238')
        .text(value || '-', x + 90, valueY);

      return y + rowHeight;
    };

    // Add cover page
    doc.addPage();

    // Add background
    doc.rect(0, 0, doc.page.width, doc.page.height).fill('#f5f5f5');

    // Add company logo and name
    try {
      const response = await axios.get(
        'https://trial-serverless.web.app/logo.png',
        {
          responseType: 'arraybuffer',
        },
      );
      const logoBuffer = Buffer.from(response.data, 'binary');

      // Add Logo
      doc.image(logoBuffer, (doc.page.width - 80) / 2, 100, {
        fit: [80, 80],
      });
    } catch (error) {
      // Fallback if logo loading fails
      doc
        .save()
        .translate(doc.page.width / 2, 140)
        .rect(-40, -40, 80, 80)
        .fill('#1a237e');
    }

    // Add company name
    doc
      .font('Helvetica-Bold')
      .fontSize(18)
      .fillColor('#1a237e')
      .text(
        'UNIGUARD',
        (doc.page.width - doc.widthOfString('UNIGUARD')) / 2,
        190,
      );

    // Add title
    doc.font('Helvetica-Bold').fontSize(18).fillColor('#1a237e');

    const titleWidth = doc.widthOfString('ACTIVITY LOG REPORT');
    doc.text('ACTIVITY LOG REPORT', (doc.page.width - titleWidth) / 2, 240);

    // Moved metadata section up (from 350 to 300)
    const metadataY = 300;
    doc.font('Helvetica').fontSize(12).fillColor('#546e7a');

    // Add active filters section
    doc
      .font('Helvetica-Bold')
      .fontSize(14)
      .fillColor('#1a237e')
      .text('Active Filters', 50, metadataY);

    doc
      .moveTo(50, metadataY + 25)
      .lineTo(545, metadataY + 25)
      .lineWidth(1)
      .strokeColor('#1a237e')
      .stroke();

    let filterY = metadataY + 30;

    // Display active filters in a clean table format
    const leftFilters = [
      {
        key: 'startDate',
        label: 'Start Date:',
        valueFunc: () =>
          filters.startDate
            ? `${dayjs(filters.startDate).format('MMMM D, YYYY')}`
            : 'All Dates',
      },
      {
        key: 'endDate',
        label: 'End Date:',
        valueFunc: () =>
          filters.endDate
            ? `${dayjs(filters.endDate).format('MMMM D, YYYY')}`
            : 'All Dates',
      },
      {
        key: 'branch',
        label: 'Branch:',
        valueFunc: () =>
          filters.branch ? `${filters.branch.branch_name}` : 'All Branches',
      },
      {
        key: 'role',
        label: 'Role:',
        valueFunc: () =>
          filters.role ? `${filters.role.role_name}` : 'All Roles',
      },
      {
        key: 'user',
        label: 'User:',
        valueFunc: () => (filters.user ? `${filters.user.name}` : 'All Users'),
      },
      {
        key: 'device',
        label: 'Device:',
        valueFunc: () =>
          filters.device ? `${filters.device.device_name}` : 'All Devices',
      },
    ];

    const rightFilters = [
      {
        key: 'startTime',
        label: 'Start Time:',
        valueFunc: () =>
          filters.startTime ? `${filters.startTime}` : 'All Times',
      },
      {
        key: 'endTime',
        label: 'End Time:',
        valueFunc: () => (filters.endTime ? `${filters.endTime}` : 'All Times'),
      },
      {
        key: 'user_labels',
        label: 'User Labels:',
        valueFunc: () =>
          filters.user_labels && filters.user_labels.length > 0
            ? filters.user_labels.map(label => label.label_name).join(', ')
            : 'All Labels',
      },
      {
        key: 'device_labels',
        label: 'Device Labels:',
        valueFunc: () =>
          filters.device_labels && filters.device_labels.length > 0
            ? filters.device_labels.map(label => label.label_name).join(', ')
            : 'All Labels',
      },
    ];

    // Render filters in two columns
    const rowHeight = 25;
    const leftX = 50;
    const rightX = 300;

    const maxRows = Math.max(leftFilters.length, rightFilters.length);
    for (let i = 0; i < maxRows; i++) {
      // Add left column filter
      if (i < leftFilters.length) {
        const leftFilter = leftFilters[i];
        addFieldRow(
          leftFilter.label,
          leftFilter.valueFunc(),
          leftX,
          filterY,
          rowHeight,
        );
      }

      // Add right column filter
      if (i < rightFilters.length) {
        const rightFilter = rightFilters[i];
        addFieldRow(
          rightFilter.label,
          rightFilter.valueFunc(),
          rightX,
          filterY,
          rowHeight,
        );
      }

      filterY += rowHeight;
    }

    // Add generation info
    doc.font('Helvetica').fontSize(12).fillColor('#546e7a');

    filterY = addFieldRow(
      'Report Generated:',
      dayjs(timezone.timezone_name).format('MMMM D, YYYY HH:mm:ss'),
      50,
      filterY,
    );
    filterY = addFieldRow(
      'Total Entries:',
      data.length.toString(),
      50,
      filterY,
    );

    // Add date range explanation section
    if (filters.startDate && filters.endDate) {
      filterY += 20;
      doc
        .font('Helvetica-Bold')
        .fontSize(14)
        .fillColor('#1a237e')
        .text('Data From:', 50, filterY);

      filterY += 25;

      // Calculate all days between start and end date
      const startDate = dayjs(filters.startDate);
      const endDate = dayjs(filters.endDate);
      const daysDiff = endDate.diff(startDate, 'day') + 1;
      const startTime = filters.startTime || '00:00';
      const endTime = filters.endTime || '23:59';

      // Display up to 5 days with truncation if more
      const maxDaysToShow = 3;
      const daysToShow = Math.min(daysDiff, maxDaysToShow);

      for (let i = 0; i < daysToShow; i++) {
        const currentDate = startDate.add(i, 'day');
        const dateStr = currentDate.format('YYYY-MM-DD');
        const timeRangeStr = `${dateStr} ${startTime} until ${dateStr} ${endTime}`;

        doc
          .font('Helvetica')
          .fontSize(10)
          .fillColor('#263238')
          .text(timeRangeStr, 50, filterY + i * 20);
      }

      // Add truncation indicator if there are more days
      if (daysDiff > maxDaysToShow) {
        doc
          .font('Helvetica')
          .fontSize(10)
          .fillColor('#263238')
          .text('...', 50, filterY + maxDaysToShow * 20);

        // Show the last day
        const lastDate = endDate.format('YYYY-MM-DD');
        const lastTimeRangeStr = `${lastDate} ${startTime} until ${lastDate} ${endTime}`;

        doc
          .font('Helvetica')
          .fontSize(10)
          .fillColor('#263238')
          .text(lastTimeRangeStr, 50, filterY + (maxDaysToShow + 1) * 20);

        filterY += (maxDaysToShow + 2) * 20;
      } else {
        filterY += daysToShow * 20;
      }
    }

    // Add footer to cover page
    const footerText = 'UniGuard Security System';
    const footerWidth = doc.widthOfString(footerText);
    doc
      .fontSize(10)
      .fillColor('#9e9e9e')
      .text(
        footerText,
        (doc.page.width - footerWidth) / 2,
        doc.page.height - 100,
      );

    // Configuration for items per page
    const itemsPerPage = 3;
    let currentPage = 1;
    let currentItem = 0;
    
    // Process alarm log entries in groups of 3 per page
    for (let i = 0; i < data.length; i++) {
      const log = data[i];
      currentItem++;
      
      // Add a new page for every 3 items
      if (currentItem === 1 || (currentItem - 1) % itemsPerPage === 0) {
        doc.addPage();
        currentPage++;
      }
      
      // Calculate vertical position based on item position within the page
      const itemPositionInPage = (currentItem - 1) % itemsPerPage;
      const yOffset = itemPositionInPage * 250; // Each item takes about 250 units of vertical space
      const currentY = 50 + yOffset;
      
      // Add entry header with item number
      drawSectionHeader(`Alarm Log Entry #${i + 1}`, currentY);
      
      // Add UUID right after header
      doc
        .font('Helvetica')
        .fontSize(10)
        .fillColor('#546e7a')
        .text(`UUID: ${log.uuid}`, 50, currentY + 35);
      
      // Add horizontal line after UUID
      doc
        .moveTo(50, currentY + 50)
        .lineTo(545, currentY + 50)
        .lineWidth(0.5)
        .strokeColor('#e0e0e0')
        .stroke();
      
      // Create a table-like structure for the data
      const leftColumnData = [
        { label: 'Branch', value: log.branch?.branch_name },
        { label: 'Role', value: log.role?.role_name },
        { label: 'User', value: log.user?.name },
        { label: 'Device', value: log.device?.device_name },
      ];
      
      const rightColumnData = [
        {
          label: 'Start Time',
          value: dayjs(log.start_date_time).format('YYYY-MM-DD HH:mm:ss'),
        },
        {
          label: 'End Time',
          value: dayjs(log.end_date_time).format('YYYY-MM-DD HH:mm:ss'),
        },
        { label: 'Timezone', value: log.timezone?.timezone_name },
        {
          label: 'Original Time',
          value: dayjs(log.original_submitted_time).format(
            'YYYY-MM-DD HH:mm:ss',
          ),
        },
      ];
      
      // Ensure left and right columns have equal number of items
      const maxColumnItems = Math.max(leftColumnData.length, rightColumnData.length);
      while (leftColumnData.length < maxColumnItems) {
        leftColumnData.push({ label: '', value: '' });
      }
      while (rightColumnData.length < maxColumnItems) {
        rightColumnData.push({ label: '', value: '' });
      }
      
      // Draw table content with alternating background and vertical centering
      let rowY = currentY + 70;
      for (let j = 0; j < maxColumnItems; j++) {
        const rowHeight = 25;
        
        // Draw alternating background
        if (j % 2 === 0) {
          doc.fillColor('#fafafa').rect(50, rowY, 495, rowHeight).fill();
        }
        
        // Draw left column data
        if (j < leftColumnData.length && leftColumnData[j].label) {
          addFieldRow(
            leftColumnData[j].label + ':',
            leftColumnData[j].value,
            50,
            rowY,
            rowHeight,
          );
        }
        
        // Draw right column data
        if (j < rightColumnData.length && rightColumnData[j].label) {
          addFieldRow(
            rightColumnData[j].label + ':',
            rightColumnData[j].value || '-',
            300,
            rowY,
            rowHeight,
          );
        }
        
        rowY += rowHeight;
      }
      
      // Add location data in a second row of columns if available
      const locationLeftData = [
        { label: 'Start Latitude', value: log.start_latitude?.toString() },
        { label: 'Start Longitude', value: log.start_longitude?.toString() },
      ];
      
      const locationRightData = [
        { label: 'End Latitude', value: log.end_latitude?.toString() },
        { label: 'End Longitude', value: log.end_longitude?.toString() },
      ];
      
      // Ensure location columns have equal number of items
      const maxLocationItems = Math.max(locationLeftData.length, locationRightData.length);
      while (locationLeftData.length < maxLocationItems) {
        locationLeftData.push({ label: '', value: '' });
      }
      while (locationRightData.length < maxLocationItems) {
        locationRightData.push({ label: '', value: '' });
      }
      
      // Draw location data
      for (let j = 0; j < maxLocationItems; j++) {
        const rowHeight = 25;
        
        // Draw alternating background
        if (j % 2 === 0) {
          doc.fillColor('#fafafa').rect(50, rowY, 495, rowHeight).fill();
        }
        
        // Draw left column data
        if (j < locationLeftData.length && locationLeftData[j].label) {
          addFieldRow(
            locationLeftData[j].label + ':',
            locationLeftData[j].value,
            50,
            rowY,
            rowHeight,
          );
        }
        
        // Draw right column data
        if (j < locationRightData.length && locationRightData[j].label) {
          addFieldRow(
            locationRightData[j].label + ':',
            locationRightData[j].value || '-',
            300,
            rowY,
            rowHeight,
          );
        }
        
        rowY += rowHeight;
      }
    }
    
    // Add page numbers to all content pages
    const totalPages = Math.ceil(data.length / itemsPerPage) + 1; // +1 for cover page
    for (let i = 0; i < doc.bufferedPageRange().count; i++) {
      if (i === 0) continue; // Skip cover page
      
      doc.switchToPage(i);
      const pageText = `Page ${i + 1} of ${totalPages}`;
      const pageWidth = doc.widthOfString(pageText);
      doc
        .font('Helvetica')
        .fontSize(10)
        .fillColor('#9e9e9e')
        .text(pageText, (doc.page.width - pageWidth) / 2, doc.page.height - 30);
    }

    // Finalize the PDF
    doc.end();

    return new Promise(resolve => {
      doc.on('end', () => {
        const buffer = Buffer.concat(buffers);
        resolve({
          buffer: buffer,
          filename: `activity-log-${dayjs().format('YYYYMMDD-HHmmss')}.pdf`,
        });
      });
    });
  }

  /**
   * Generates a spreadsheet for a single alarm log entry
   * @param {LogAlarm} alarmLog - The alarm log data to generate spreadsheet for
   * @param timezone
   * @returns {Promise<{buffer: Buffer, filename: string}>} Spreadsheet buffer and filename
   */
  async generateSpreadsheetById(
    alarmLog: LogAlarm,
    timezone: Timezone,
  ): Promise<{ buffer: Buffer; filename: string }> {
    let currentRow = 1;
    // Create a new workbook and worksheet
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Activity Log');

    // Add title and styling
    worksheet.mergeCells('A1:G1');
    const titleCell = worksheet.getCell('A1');
    titleCell.value = 'UNIGUARD ACTIVITY LOG REPORT';
    titleCell.font = { size: 16, bold: true, color: { argb: '1A237E' } };
    titleCell.alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getRow(1).height = 30;
    currentRow++;

    // Add generation time
    worksheet.mergeCells('A2:G2');
    const timeCell = worksheet.getCell('A2');
    timeCell.value = `Generated: ${dayjs(timezone.timezone_name).format('YYYY-MM-DD HH:mm:ss')}`;
    timeCell.font = { size: 10, color: { argb: '546E7A' } };
    timeCell.alignment = { horizontal: 'center' };
    currentRow++;

    // Add UUID
    worksheet.mergeCells('A3:G3');
    const uuidCell = worksheet.getCell('A3');
    uuidCell.value = `UUID: ${alarmLog.uuid}`;
    uuidCell.font = { size: 10, color: { argb: '546E7A' } };
    currentRow++;

    // Add header row
    const headerRow = worksheet.addRow(['Field', 'Value']);
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'E3F2FD' },
    };
    currentRow++;

    // Add data rows
    const dataRows = [
      ['Branch', alarmLog.branch_name || '-'],
      ['Role', alarmLog.role_name || '-'],
      ['User', alarmLog.user_name || '-'],
      ['Device', alarmLog.device_name || '-'],
      ['Timezone', alarmLog.timezone_name || '-'],
      ['Start Latitude', alarmLog.start_latitude?.toString() || '-'],
      ['Start Longitude', alarmLog.start_longitude?.toString() || '-'],
      ['End Latitude', alarmLog.end_latitude?.toString() || '-'],
      ['End Longitude', alarmLog.end_longitude?.toString() || '-'],
      [
        'Start Time',
        dayjs(alarmLog.start_date_time).format('YYYY-MM-DD HH:mm:ss'),
      ],
      ['End Time', dayjs(alarmLog.end_date_time).format('YYYY-MM-DD HH:mm:ss')],
      [
        'Original Time',
        dayjs(alarmLog.original_submitted_time).format('YYYY-MM-DD HH:mm:ss'),
      ],
    ];

    // Add data rows with alternating colors
    dataRows.forEach((row, index) => {
      currentRow++;
      const excelRow = worksheet.addRow(row);
      // Add alternating row colors for better readability
      if (index % 2 === 1) {
        excelRow.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FAFAFA' },
        };
      }
    });

    // Set column widths for better layout
    worksheet.getColumn(1).width = 20;
    worksheet.getColumn(2).width = 50;

    // Add footer with system information
    currentRow++;
    const footerRowIndex = currentRow;
    worksheet.mergeCells(`A${footerRowIndex}:G${footerRowIndex}`);
    const footerCell = worksheet.getCell(`A${footerRowIndex}`);
    footerCell.value = 'UniGuard Security System';
    footerCell.font = { size: 10, color: { argb: '9E9E9E' } };
    footerCell.alignment = { horizontal: 'center' };

    // Generate buffer
    const buffer = await workbook.xlsx.writeBuffer();

    return {
      buffer: buffer as Buffer,
      filename: `activity-log-${alarmLog.id}-${dayjs().format('YYYYMMDD-HHmmss')}.xlsx`,
    };
  }

  /**
   * Generates a spreadsheet for multiple alarm log entries with filters
   * @param {LogAlarm[]} data - Array of alarm log data
   * @param {AlarmLogFiltersGenerateDocument} filters - Filters applied to the data
   * @param timezone
   * @returns {Promise<{buffer: Buffer, filename: string}>} Spreadsheet buffer and filename
   */
  async generateSpreadsheet(
    data: LogAlarm[],
    filters: AlarmLogFiltersGenerateDocument,
    timezone: Timezone,
  ): Promise<{ buffer: Buffer; filename: string }> {
    // Create a new workbook and worksheet
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Activity Logs');
    let currentRow = 1;

    // Add title and styling (merged across all columns)
    worksheet.mergeCells('A1:K1');
    const titleCell = worksheet.getCell('A1');
    titleCell.value = 'UNIGUARD ACTIVITY LOGS REPORT';
    titleCell.font = { size: 16, bold: true, color: { argb: '1A237E' } };
    titleCell.alignment = { horizontal: 'center', vertical: 'middle' };

    // Add generation time and total entries
    currentRow++;
    worksheet.mergeCells(`A${currentRow}:K${currentRow}`);
    const timeCell = worksheet.getCell(`A${currentRow}`);
    timeCell.value = `Generated: ${dayjs(timezone.timezone_name).format('YYYY-MM-DD HH:mm:ss')} | Total Entries: ${data.length}`;
    timeCell.font = { size: 10, color: { argb: '546E7A' } };
    timeCell.alignment = { horizontal: 'center' };

    // Add filter information
    currentRow++;
    worksheet.mergeCells(`A${currentRow}:N${currentRow}`);
    const filterTitleCell = worksheet.getCell(`A${currentRow}`);
    filterTitleCell.value = 'FILTER CRITERIA';
    filterTitleCell.font = { size: 12, bold: true, color: { argb: '1A237E' } };

    // Add filter details
    if (filters.startDate && filters.endDate) {
      currentRow++;
      worksheet.getCell(`A${currentRow}`).value = 'Date Range:';
      worksheet.getCell(`B${currentRow}`).value =
        `${filters.startDate} to ${filters.endDate}`;
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
    }

    if (filters.startTime && filters.endTime) {
      currentRow++;
      worksheet.getCell(`A${currentRow}`).value = 'Time Range:';
      worksheet.getCell(`B${currentRow}`).value =
        `${filters.startTime} to ${filters.endTime}`;
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
    }

    if (filters.branch) {
      currentRow++;
      worksheet.getCell(`A${currentRow}`).value = 'Branch:';
      worksheet.getCell(`B${currentRow}`).value = filters.branch.branch_name;
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
    }

    if (filters.role) {
      currentRow++;
      worksheet.getCell(`A${currentRow}`).value = 'Role:';
      worksheet.getCell(`B${currentRow}`).value = filters.role.role_name;
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
    }

    if (filters.user) {
      currentRow++;
      worksheet.getCell(`A${currentRow}`).value = 'User:';
      worksheet.getCell(`B${currentRow}`).value = filters.user.name;
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
    } else if (filters.user_labels.length > 0) {
      currentRow++;
      worksheet.getCell(`A${currentRow}`).value = 'User Labels:';
      worksheet.getCell(`B${currentRow}`).value = filters.user_labels
        .map(label => label.label_name)
        .join(', ');
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
    }

    if (filters.device) {
      worksheet.getCell(`A${currentRow}`).value = 'Device:';
      worksheet.getCell(`B${currentRow}`).value = filters.device.device_name;
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
      currentRow++;
    } else if (filters.device_labels.length > 0) {
      currentRow++;
      worksheet.getCell(`A${currentRow}`).value = 'Device Labels:';
      worksheet.getCell(`B${currentRow}`).value = filters.device_labels
        .map(label => label.label_name)
        .join(', ');
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
    }

    // Add date range explanation section
    if (
      filters.startDate &&
      filters.endDate &&
      filters.startTime &&
      filters.endTime
    ) {
      currentRow += 2;
      let startRow = currentRow;
      let endRow = currentRow;
      const cellDateRange = worksheet.getCell(`A${currentRow}`);
      cellDateRange.value = 'Date Range:';
      cellDateRange.font = { bold: true };
      cellDateRange.alignment = { horizontal: 'left', vertical: 'middle' };

      // Calculate the number of days between the start and end date
      const startDate = dayjs(filters.startDate);
      const endDate = dayjs(filters.endDate);
      const daysDiff = endDate.diff(startDate, 'day') + 1;
      const startTime = filters.startTime || '00:00';
      const endTime = filters.endTime || '23:59';

      const maxDaysToShow = 3;
      const daysToShow = Math.min(daysDiff, maxDaysToShow);

      for (let i = 0; i < daysToShow; i++) {
        if (i > 0) {
          currentRow++;
        }
        endRow = currentRow;

        const currentDate = startDate.add(i, 'day');
        const dateStr = currentDate.format('YYYY-MM-DD');
        worksheet.getCell(`B${currentRow}`).value =
          `${dateStr} ${startTime} until ${dateStr} ${endTime}`;
      }

      if (daysDiff > maxDaysToShow) {
        currentRow++;
        worksheet.getCell(`B${currentRow}`).value = '...';

        // Show last day
        currentRow++;
        endRow = currentRow;
        const dateStr = endDate.format('YYYY-MM-DD');
        worksheet.getCell(`B${currentRow}`).value =
          `${dateStr} ${startTime} until ${dateStr} ${endTime}`;
      }

      // Merge the date range cells
      worksheet.mergeCells(`A${startRow}:A${endRow}`);
    }

    // Add table headers
    const headers = [
      'UUID',
      'Branch',
      'Role',
      'User',
      'Device',
      'Timezone',
      'Start Latitude',
      'Start Longitude',
      'End Latitude',
      'End Longitude',
      'Start Time',
      'End Time',
      'Original Time',
    ];

    currentRow += 2;
    worksheet.addRow(2);
    const headerRow = worksheet.addRow(headers);
    headerRow.font = { bold: true, color: { argb: 'FFFFFF' } };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '1A237E' },
    };
    headerRow.alignment = { horizontal: 'center' };

    // Add data rows
    currentRow++;
    data.forEach(activityLog => {
      worksheet.getCell(currentRow, 1).value = activityLog.uuid;
      worksheet.getCell(currentRow, 2).value =
        activityLog.branch?.branch_name || '-';
      worksheet.getCell(currentRow, 3).value =
        activityLog.role?.role_name || '-';
      worksheet.getCell(currentRow, 4).value = activityLog.user?.name || '-';
      worksheet.getCell(currentRow, 5).value =
        activityLog.device?.device_name || '-';
      worksheet.getCell(currentRow, 6).value =
        activityLog.timezone?.timezone_name || '-';
      worksheet.getCell(currentRow, 7).value =
        activityLog.start_latitude || '-';
      worksheet.getCell(currentRow, 8).value =
        activityLog.start_longitude || '-';
      worksheet.getCell(currentRow, 9).value = activityLog.end_latitude || '-';
      worksheet.getCell(currentRow, 10).value =
        activityLog.end_longitude || '-';
      worksheet.getCell(currentRow, 11).value = dayjs(
        activityLog.start_date_time,
      ).format('YYYY-MM-DD HH:mm:ss');
      worksheet.getCell(currentRow, 12).value = dayjs(
        activityLog.end_date_time,
      ).format('YYYY-MM-DD HH:mm:ss');
      worksheet.getCell(currentRow, 13).value = dayjs(
        activityLog.original_submitted_time,
      ).format('YYYY-MM-DD HH:mm:ss');

      // Apply alternating row colors
      if (currentRow % 2 === 0) {
        for (let i = 1; i <= headers.length; i++) {
          worksheet.getCell(currentRow, i).fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'F5F5F5' },
          };
        }
      }

      currentRow++;
    });

    // Set column widths
    worksheet.getColumn(1).width = 36; // UUID
    worksheet.getColumn(2).width = 20; // Branch
    worksheet.getColumn(3).width = 20; // Role
    worksheet.getColumn(4).width = 20; // User
    worksheet.getColumn(5).width = 20; // Device
    worksheet.getColumn(6).width = 20; // Timezone
    worksheet.getColumn(7).width = 20; // Start Latitude
    worksheet.getColumn(8).width = 20; // Start Longitude
    worksheet.getColumn(9).width = 20; // End Latitude
    worksheet.getColumn(10).width = 20; // End Longitude
    worksheet.getColumn(11).width = 20; // Start Time
    worksheet.getColumn(12).width = 20; // End Time
    worksheet.getColumn(13).width = 20; // Original Time

    // Add footer
    currentRow += 2;
    const footerRowIndex = currentRow;
    worksheet.mergeCells(`A${footerRowIndex}:N${footerRowIndex}`);
    const footerCell = worksheet.getCell(`A${footerRowIndex}`);
    footerCell.value = 'UniGuard Security System';
    footerCell.font = { size: 10, color: { argb: '9E9E9E' } };
    footerCell.alignment = { horizontal: 'center' };

    // Generate buffer
    const buffer = await workbook.xlsx.writeBuffer();

    return {
      buffer: buffer as Buffer,
      filename: `activity-logs-${dayjs().format('YYYYMMDD-HHmmss')}.xlsx`,
    };
  }

  /**
   * Creates a new PDF document with custom settings
   * @returns {PDFDocument} A new PDFKit document instance
   */
  private createPdfDoc = () => {
    return new PDFDocument({
      size: 'A4',
      autoFirstPage: false,
      margins: {
        top: 10,
        bottom: 10,
        left: 10,
        right: 10,
      },
      info: {
        Title: 'Activity Log Report',
        Author: 'UniGuard System',
        Creator: 'UniGuard',
      },
    });
  };
}
