import { Injectable } from '@nestjs/common';
import { LogUserDevice } from '../../../database/entities/log-user-device.entity';
import axios from 'axios';
import * as dayjs from 'dayjs';
import * as timezone from 'dayjs/plugin/timezone';
import * as ExcelJS from 'exceljs';
import * as PDFDocument from 'pdfkit';
import { Branch } from '../../../database/entities/branch.entity';
import { User } from '../../../database/entities/user.entity';
import { Label } from '../../../database/entities/label.entity';
import { Device } from '../../../database/entities/device.entity';
import { Timezone } from '../../../database/entities/timezone.entity';

dayjs.extend(timezone);

export interface SignInOutLogFiltersGenerateDocument {
  startDate: string | null;
  endDate: string | null;
  startTime: string | null;
  endTime: string | null;
  branch: Branch | null;
  user: User | null;
  user_labels: Label[];
  device: Device | null;
  device_labels: Label[];
}

@Injectable()
export class SignInOutLogGenerateDocumentService {
  private createPdfDoc = () => {
    return new PDFDocument({
      size: 'A4',
      autoFirstPage: false,
      margins: {
        top: 10,
        bottom: 10,
        left: 10,
        right: 10,
      },
      info: {
        Title: 'Sign In/Out Log Report',
        Author: 'UniGuard System',
        Creator: 'UniGuard',
      },
    });
  };

  constructor() {}

  /**
   * Generates a PDF document for a single sign in/out log entry
   * Creates a professional report with detailed information and photo (if available)
   *
   * @param signInOutLog - Single sign in/out log entry to generate report for
   * @param timezone
   * @returns Promise resolving to an object containing the PDF buffer and filename
   * @throws {Error} If there's an issue generating the PDF
   *
   * @remarks
   * The generated PDF includes:
   * - Company logo and report metadata
   * - Detailed sign in/out log information in table format
   * - Professional layout and styling
   */
  async generatePDFById(
    signInOutLog: LogUserDevice,
    timezone: Timezone,
  ): Promise<{
    buffer: Buffer;
    filename: string;
  }> {
    // Create a new PDF document with custom options
    const doc = this.createPdfDoc();
    // Create a buffer to store the PDF
    const buffers: Buffer[] = [];
    doc.on('data', buffers.push.bind(buffers));

    // Helper functions for PDF formatting
    const drawSectionHeader = (text: string, y: number) => {
      doc
        .fontSize(16)
        .font('Helvetica-Bold')
        .fillColor('#1a237e')
        .text(text, 50, y);

      doc
        .moveTo(50, y + 25)
        .lineTo(545, y + 25)
        .lineWidth(1)
        .strokeColor('#1a237e')
        .stroke();
    };

    const addFieldRow = (
      label: string,
      value: string | null | undefined,
      x: number,
      y: number,
      rowHeight: number = 25,
    ) => {
      const labelY = y + (rowHeight - doc.currentLineHeight()) / 2;
      const valueY = y + (rowHeight - doc.currentLineHeight()) / 2;

      doc
        .font('Helvetica-Bold')
        .fontSize(10)
        .fillColor('#546e7a')
        .text(label, x, labelY);

      doc
        .font('Helvetica')
        .fontSize(10)
        .fillColor('#263238')
        .text(value || '-', x + 90, valueY);

      return y + rowHeight;
    };

    // Add Page
    doc.addPage();

    // Add company logo and name in header
    try {
      const response = await axios.get(
        'https://trial-serverless.web.app/logo.png',
        {
          responseType: 'arraybuffer',
        },
      );
      const logoBuffer = Buffer.from(response.data, 'binary');

      // Then add the image
      doc.image(logoBuffer, 50, 50, {
        fit: [50, 50],
      });
    } catch (error) {
      console.error('Error loading logo:', error);
      // Fallback if logo loading fails
      doc.save().translate(75, 75).rect(-25, -25, 50, 50).fill('#1a237e');
    }

    // Add company name next to logo
    doc
      .font('Helvetica-Bold')
      .fontSize(20)
      .fillColor('#1a237e')
      .text('UNIGUARD', 110, 65);

    // Add generation time in header
    doc
      .font('Helvetica')
      .fontSize(10)
      .fillColor('#546e7a')
      .text(
        `Generated: ${dayjs().tz(timezone.timezone_name).format('YYYY-MM-DD HH:mm')}`,
        350,
        65,
      );

    // Add title and header
    drawSectionHeader('Sign In/Out Log Details', 120);

    // Add UUID right after header
    doc
      .font('Helvetica')
      .fontSize(10)
      .fillColor('#546e7a')
      .text(`UUID: ${signInOutLog.uuid}`, 50, 155);

    // Add horizontal line after UUID
    doc
      .moveTo(50, 170)
      .lineTo(545, 170)
      .lineWidth(0.5)
      .strokeColor('#e0e0e0')
      .stroke();

    // Add entry details with improved layout
    let currentY = 190;

    // Create dayjs object with timezone for reuse
    const originalSubmittedTime = dayjs(signInOutLog.event_time).tz(
      signInOutLog.timezone_name,
    );

    // Create a table-like structure for the data
    const leftColumnData = [
      {
        label: 'Date & Time',
        value: originalSubmittedTime.format('YYYY-MM-DD HH:mm'),
      },
      { label: 'Main Branch', value: signInOutLog.parent_branch?.branch_name },
      { label: 'User', value: signInOutLog.user_name },
      { label: 'Device', value: signInOutLog.device_name },
      { label: 'Event Type', value: signInOutLog.user_log_type },
    ];

    const rightColumnData = [
      { label: 'Timezone', value: signInOutLog.timezone_name },
      { label: 'Latitude', value: signInOutLog.latitude?.toString() },
      { label: 'Longitude', value: signInOutLog.longitude?.toString() },
    ];

    // Draw table content with alternating background and vertical centering
    leftColumnData.forEach((item, i) => {
      const rowHeight = 30; // Increased row height for better spacing

      // Draw alternating background
      if (i % 2 === 0) {
        doc.fillColor('#fafafa').rect(50, currentY, 495, rowHeight).fill();
      }

      // Draw left column data
      currentY = addFieldRow(
        item.label + ':',
        item.value,
        50,
        currentY,
        rowHeight,
      );

      // Draw right column data if available
      if (i < rightColumnData.length) {
        addFieldRow(
          rightColumnData[i].label + ':',
          rightColumnData[i].value,
          300,
          currentY - rowHeight,
          rowHeight,
        );
      }
    });

    // Add footer
    const footerText = 'UniGuard Security System';
    const footerWidth = doc.widthOfString(footerText);
    doc
      .fontSize(10)
      .fillColor('#9e9e9e')
      .text(
        footerText,
        (doc.page.width - footerWidth) / 2,
        doc.page.height - 50,
      );

    // Finalize the PDF
    doc.end();

    return new Promise(resolve => {
      doc.on('end', () => {
        const buffer = Buffer.concat(buffers);
        resolve({
          buffer: buffer,
          filename: `sign-in-out-log-${dayjs().tz(timezone.timezone_name).format('YYYYMMDD-HHmmss')}.pdf`,
        });
      });
    });
  }

  /**
   * Generates a spreadsheet document for a single sign in/out log entry
   * Creates a professional report with detailed information in Excel format
   *
   * @param signInOutLog - Single sign in/out log entry to generate report for
   * @param timezone
   * @returns Promise resolving to an object containing the spreadsheet buffer and filename
   * @throws {Error} If there's an issue generating the spreadsheet
   *
   * @remarks
   * The generated spreadsheet includes:
   * - Report title and metadata
   * - Detailed sign in/out log information in table format
   * - Professional formatting and styling
   */
  async generateSpreadsheetById(
    signInOutLog: LogUserDevice,
    timezone: Timezone,
  ): Promise<{
    buffer: Buffer;
    filename: string;
  }> {
    let currentRow = 1;
    // Create a new workbook and worksheet
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Sign In-Out Log');

    // Add title and styling
    worksheet.mergeCells('A1:G1');
    const titleCell = worksheet.getCell('A1');
    titleCell.value = 'UNIGUARD SIGN IN/OUT LOG REPORT';
    titleCell.font = { size: 16, bold: true, color: { argb: '1A237E' } };
    titleCell.alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getRow(1).height = 30;
    currentRow++;

    // Add generation time
    worksheet.mergeCells('A2:G2');
    const timeCell = worksheet.getCell('A2');
    timeCell.value = `Generated: ${dayjs().tz(timezone.timezone_name).format('YYYY-MM-DD HH:mm')}`;
    timeCell.font = { size: 10, color: { argb: '546E7A' } };
    timeCell.alignment = { horizontal: 'center' };
    currentRow++;

    // Add UUID
    worksheet.mergeCells('A3:G3');
    const uuidCell = worksheet.getCell('A3');
    uuidCell.value = `UUID: ${signInOutLog.uuid}`;
    uuidCell.font = { size: 10, color: { argb: '546E7A' } };
    currentRow++;

    // Add header row
    const headers = ['Field', 'Value'];
    const headerRow = worksheet.addRow(headers);
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'E3F2FD' },
    };
    currentRow++;

    // Create dayjs object with timezone for reuse
    const originalSubmittedTime = dayjs(signInOutLog.event_time).tz(
      signInOutLog.timezone_name,
    );

    // Add data rows
    const dataRows = [
      [
        'Date & Time',
        originalSubmittedTime.format('YYYY-MM-DD HH:mm'),
      ],
      ['Main Branch', signInOutLog.parent_branch?.branch_name || '-'],
      ['User', signInOutLog.user_name || '-'],
      ['Device', signInOutLog.device_name || '-'],
      ['Event Type', signInOutLog.user_log_type || '-'],
      ['Timezone', signInOutLog.timezone_name || '-'],
      ['Latitude', signInOutLog.latitude?.toString() || '-'],
      ['Longitude', signInOutLog.longitude?.toString() || '-'],
    ];

    // Add data rows with alternating colors
    dataRows.forEach((row, index) => {
      currentRow++;
      const excelRow = worksheet.addRow(row);
      // Add alternating row colors for better readability
      if (index % 2 === 1) {
        excelRow.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FAFAFA' },
        };
      }
    });

    // Set column widths for better layout
    worksheet.getColumn(1).width = 20;
    worksheet.getColumn(2).width = 50;

    // Add footer with system information
    currentRow++;
    const footerRowIndex = currentRow;
    worksheet.mergeCells(`A${footerRowIndex}:G${footerRowIndex}`);
    const footerCell = worksheet.getCell(`A${footerRowIndex}`);
    footerCell.value = 'UniGuard Security System';
    footerCell.font = { size: 10, color: { argb: '9E9E9E' } };
    footerCell.alignment = { horizontal: 'center' };

    // Generate buffer
    const buffer = await workbook.xlsx.writeBuffer();

    return {
      buffer: buffer as Buffer,
      filename: `sign-in-out-log-${signInOutLog.id}-${dayjs().tz(timezone.timezone_name).format('YYYYMMDD-HHmmss')}.xlsx`,
    };
  }

  /**
   * Generates a PDF document from multiple sign in/out log entries
   * Creates a professional report with cover page and detailed entries
   *
   * @param data - Array of sign in/out log entries
   * @param filters - Object containing all applied filters
   * @param timezone
   * @returns Promise resolving to an object containing the PDF buffer and filename
   * @throws {Error} If there's an issue generating the PDF
   *
   * @remarks
   * The generated PDF includes:
   * - Cover page with company logo and report metadata
   * - Detailed filter information
   * - Individual sign in/out log entries
   * - Professional layout and styling
   */
  async generatePDF(
    data: LogUserDevice[],
    filters: SignInOutLogFiltersGenerateDocument,
    timezone: Timezone,
  ): Promise<{ buffer: Buffer; filename: string }> {
    // Create a new PDF document with custom options
    const doc = this.createPdfDoc();
    // Create a buffer to store the PDF
    const buffers: Buffer[] = [];
    doc.on('data', buffers.push.bind(buffers));

    // Helper function to create section headers
    const drawSectionHeader = (text: string, y: number) => {
      doc
        .fontSize(16)
        .font('Helvetica-Bold')
        .fillColor('#1a237e')
        .text(text, 50, y);

      doc
        .moveTo(50, y + 25)
        .lineTo(545, y + 25)
        .lineWidth(1)
        .strokeColor('#1a237e')
        .stroke();
    };

    // Helper function to add field rows with vertical centering
    const addFieldRow = (
      label: string,
      value: string | null | undefined,
      x: number,
      y: number,
      rowHeight: number = 25,
    ) => {
      const labelY = y + (rowHeight - doc.currentLineHeight()) / 2;
      const valueY = y + (rowHeight - doc.currentLineHeight()) / 2;

      doc
        .font('Helvetica-Bold')
        .fontSize(10)
        .fillColor('#546e7a')
        .text(label, x, labelY);

      doc
        .font('Helvetica')
        .fontSize(10)
        .fillColor('#263238')
        .text(value || '-', x + 90, valueY);

      return y + rowHeight;
    };

    // Add cover page
    doc.addPage();

    // Add background
    doc.rect(0, 0, doc.page.width, doc.page.height).fill('#f5f5f5');

    // Add company logo and name
    try {
      const response = await axios.get(
        'https://trial-serverless.web.app/logo.png',
        {
          responseType: 'arraybuffer',
        },
      );
      const logoBuffer = Buffer.from(response.data, 'binary');

      // Add Logo
      doc.image(logoBuffer, (doc.page.width - 80) / 2, 100, {
        fit: [80, 80],
      });
    } catch (error) {
      // Fallback if logo loading fails
      doc
        .save()
        .translate(doc.page.width / 2, 140)
        .rect(-40, -40, 80, 80)
        .fill('#1a237e');
    }

    // Add company name
    doc
      .font('Helvetica-Bold')
      .fontSize(18)
      .fillColor('#1a237e')
      .text(
        'UNIGUARD',
        (doc.page.width - doc.widthOfString('UNIGUARD')) / 2,
        190,
      );

    // Add title
    doc.font('Helvetica-Bold').fontSize(18).fillColor('#1a237e');

    const titleWidth = doc.widthOfString('SIGN IN/OUT LOG REPORT');
    doc.text('SIGN IN/OUT LOG REPORT', (doc.page.width - titleWidth) / 2, 240);

    // Moved metadata section up (from 350 to 300)
    const metadataY = 300;
    doc.font('Helvetica').fontSize(12).fillColor('#546e7a');

    // Add active filters section
    doc
      .font('Helvetica-Bold')
      .fontSize(14)
      .fillColor('#1a237e')
      .text('Active Filters', 50, metadataY);

    doc
      .moveTo(50, metadataY + 25)
      .lineTo(545, metadataY + 25)
      .lineWidth(1)
      .strokeColor('#1a237e')
      .stroke();

    let filterY = metadataY + 30;

    // Display active filters in a clean table format
    const leftFilters = [
      {
        key: 'startDate',
        label: 'Start Date:',
        valueFunc: () =>
          filters.startDate
            ? `${dayjs(filters.startDate).format('MMMM D, YYYY')}`
            : 'All Dates',
      },
      {
        key: 'endDate',
        label: 'End Date:',
        valueFunc: () =>
          filters.endDate
            ? `${dayjs(filters.endDate).format('MMMM D, YYYY')}`
            : 'All Dates',
      },
      {
        key: 'branch',
        label: 'Branch:',
        valueFunc: () =>
          filters.branch ? `${filters.branch.branch_name}` : 'All Branches',
      },
      {
        key: 'user',
        label: 'User:',
        valueFunc: () => (filters.user ? `${filters.user.name}` : 'All Users'),
      },
      {
        key: 'device',
        label: 'Device:',
        valueFunc: () =>
          filters.device ? `${filters.device.device_name}` : 'All Devices',
      },
    ];

    const rightFilters = [
      {
        key: 'startTime',
        label: 'Start Time:',
        valueFunc: () =>
          filters.startTime ? `${filters.startTime}` : 'All Times',
      },
      {
        key: 'endTime',
        label: 'End Time:',
        valueFunc: () => (filters.endTime ? `${filters.endTime}` : 'All Times'),
      },
      {
        key: 'user_labels',
        label: 'User Labels:',
        valueFunc: () =>
          filters.user_labels && filters.user_labels.length > 0
            ? filters.user_labels.map(label => label.label_name).join(', ')
            : 'All Labels',
      },
      {
        key: 'device_labels',
        label: 'Device Labels:',
        valueFunc: () =>
          filters.device_labels && filters.device_labels.length > 0
            ? filters.device_labels.map(label => label.label_name).join(', ')
            : 'All Labels',
      },
    ];

    // Render filters in two columns
    const rowHeight = 25;
    const leftX = 50;
    const rightX = 300;

    const maxRows = Math.max(leftFilters.length, rightFilters.length);
    for (let i = 0; i < maxRows; i++) {
      // Add left column filter
      if (i < leftFilters.length) {
        const leftFilter = leftFilters[i];
        addFieldRow(
          leftFilter.label,
          leftFilter.valueFunc(),
          leftX,
          filterY,
          rowHeight,
        );
      }

      // Add right column filter
      if (i < rightFilters.length) {
        const rightFilter = rightFilters[i];
        addFieldRow(
          rightFilter.label,
          rightFilter.valueFunc(),
          rightX,
          filterY,
          rowHeight,
        );
      }

      filterY += rowHeight;
    }

    // Add generation info
    doc.font('Helvetica').fontSize(12).fillColor('#546e7a');

    filterY = addFieldRow(
      'Report Generated:',
      dayjs().tz(timezone.timezone_name).format('MMMM D, YYYY HH:mm'),
      50,
      filterY,
    );
    filterY = addFieldRow(
      'Total Entries:',
      data.length.toString(),
      50,
      filterY,
    );

    // Add date range explanation section
    if (filters.startDate && filters.endDate) {
      filterY += 20;
      doc
        .font('Helvetica-Bold')
        .fontSize(14)
        .fillColor('#1a237e')
        .text('Data From:', 50, filterY);

      filterY += 25;

      // Calculate all days between start and end date
      const startDate = dayjs(filters.startDate);
      const endDate = dayjs(filters.endDate);
      const daysDiff = endDate.diff(startDate, 'day') + 1;
      const startTime = filters.startTime || '00:00';
      const endTime = filters.endTime || '23:59';

      // Display up to 5 days with truncation if more
      const maxDaysToShow = 3;
      const daysToShow = Math.min(daysDiff, maxDaysToShow);

      for (let i = 0; i < daysToShow; i++) {
        const currentDate = startDate.add(i, 'day');
        const dateStr = currentDate.format('YYYY-MM-DD');
        const timeRangeStr = `${dateStr} ${startTime} until ${dateStr} ${endTime}`;

        doc
          .font('Helvetica')
          .fontSize(10)
          .fillColor('#263238')
          .text(timeRangeStr, 50, filterY + i * 20);
      }

      // Add truncation indicator if there are more days
      if (daysDiff > maxDaysToShow) {
        doc
          .font('Helvetica')
          .fontSize(10)
          .fillColor('#263238')
          .text('...', 50, filterY + maxDaysToShow * 20);

        // Show the last day
        const lastDate = endDate.format('YYYY-MM-DD');
        const lastTimeRangeStr = `${lastDate} ${startTime} until ${lastDate} ${endTime}`;

        doc
          .font('Helvetica')
          .fontSize(10)
          .fillColor('#263238')
          .text(lastTimeRangeStr, 50, filterY + (maxDaysToShow + 1) * 20);

        filterY += (maxDaysToShow + 2) * 20;
      } else {
        filterY += daysToShow * 20;
      }
    }

    // Add footer to cover page
    const footerText = 'UniGuard Security System';
    const footerWidth = doc.widthOfString(footerText);
    doc
      .fontSize(10)
      .fillColor('#9e9e9e')
      .text(
        footerText,
        (doc.page.width - footerWidth) / 2,
        doc.page.height - 100,
      );

    // Process each sign in/out log entry
    let itemsPerPage = 3; // Number of items per page
    let currentItem = 0;
    let currentY = 0;
    
    for (const [index, log] of data.entries()) {
      // Add new page only for first item or when we've reached the items per page limit
      if (currentItem % itemsPerPage === 0) {
        doc.addPage();
        currentY = 50; // Reset Y position for new page
      } else {
        // Add some spacing between items on the same page
        currentY += 30;
      }

      // Add item header with UUID
      drawSectionHeader(`Sign In/Out Log Entry #${index + 1}`, currentY);
      currentY += 35; // Move down after section header

      // Add UUID right after header
      doc
        .font('Helvetica')
        .fontSize(10)
        .fillColor('#546e7a')
        .text(`UUID: ${log.uuid}`, 50, currentY);
      currentY += 15;

      // Add horizontal line after UUID
      doc
        .moveTo(50, currentY)
        .lineTo(545, currentY)
        .lineWidth(0.5)
        .strokeColor('#e0e0e0')
        .stroke();
      currentY += 20;

      // Create dayjs object with timezone for reuse
      const originalSubmittedTime = dayjs(log.event_time).tz(
        log.timezone_name,
      );

      // Create a table-like structure for the data
      const leftColumnData = [
        {
          label: 'Date & Time',
          value: originalSubmittedTime.format('YYYY-MM-DD HH:mm'),
        },
        { label: 'Main Branch', value: log.parent_branch?.branch_name },
        { label: 'User', value: log.user_name },
        { label: 'Device', value: log.device_name },
        { label: 'Event Type', value: log.user_log_type },
      ];

      const rightColumnData = [
        { label: 'Timezone', value: log.timezone_name },
        { label: 'Latitude', value: log.latitude?.toString() },
        { label: 'Longitude', value: log.longitude?.toString() },
      ];

      // Draw table content with alternating background and vertical centering
      leftColumnData.forEach((item, i) => {
        const rowHeight = 25; // Slightly reduced row height to fit more content

        // Draw alternating background
        if (i % 2 === 0) {
          doc.fillColor('#fafafa').rect(50, currentY, 495, rowHeight).fill();
        }

        // Draw left column data
        currentY = addFieldRow(
          item.label + ':',
          item.value,
          50,
          currentY,
          rowHeight,
        );

        // Draw right column data if available
        if (i < rightColumnData.length) {
          addFieldRow(
            rightColumnData[i].label + ':',
            rightColumnData[i].value,
            300,
            currentY - rowHeight,
            rowHeight,
          );
        }
      });
      
      // Increment current item counter
      currentItem++;
      
      // Check if we're at the last item on a page or the last item overall
      if (currentItem % itemsPerPage === 0 || index === data.length - 1) {
        // Add page number at fixed position from bottom
        const pageNumber = Math.ceil((index + 1) / itemsPerPage) + 1; // +1 for cover page
        const totalPages = Math.ceil(data.length / itemsPerPage) + 1; // +1 for cover page
        const pageText = `Page ${pageNumber} of ${totalPages}`;
        const pageWidth = doc.widthOfString(pageText);
        doc
          .font('Helvetica')
          .fontSize(10)
          .fillColor('#9e9e9e')
          .text(pageText, (doc.page.width - pageWidth) / 2, doc.page.height - 30);
      }
    }

    // Finalize the PDF
    doc.end();

    return new Promise(resolve => {
      doc.on('end', () => {
        const buffer = Buffer.concat(buffers);
        resolve({
          buffer: buffer,
          filename: `sign-in-out-logs-${dayjs().tz(timezone.timezone_name).format('YYYYMMDD-HHmmss')}.pdf`,
        });
      });
    });
  }

  /**
   * Generates a spreadsheet document from multiple sign in/out log entries
   * Creates a professional report with metadata and detailed entries
   *
   * @param data - Array of sign in/out log entries
   * @param filters - Object containing all applied filters
   * @param timezone
   * @returns Promise resolving to an object containing the spreadsheet buffer and filename
   * @throws {Error} If there's an issue generating the spreadsheet
   *
   * @remarks
   * The generated spreadsheet includes:
   * - Cover sheet with report metadata and filters
   * - Data sheet with all log entries
   * - Professional formatting and styling
   */
  async generateSpreadsheet(
    data: LogUserDevice[],
    filters: SignInOutLogFiltersGenerateDocument,
    timezone: Timezone,
  ): Promise<{ buffer: Buffer; filename: string }> {
    // Create a new workbook and worksheet
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Sign In-Out Logs');
    let currentRow = 1;

    // Add title and styling (merged across all columns)
    worksheet.mergeCells('A1:I1');
    const titleCell = worksheet.getCell('A1');
    titleCell.value = 'UNIGUARD SIGN IN/OUT LOGS REPORT';
    titleCell.font = { size: 16, bold: true, color: { argb: '1A237E' } };
    titleCell.alignment = { horizontal: 'center', vertical: 'middle' };

    // Add generation time and total entries
    currentRow++;
    worksheet.mergeCells(`A${currentRow}:I${currentRow}`);
    const timeCell = worksheet.getCell(`A${currentRow}`);
    timeCell.value = `Generated: ${dayjs().tz(timezone.timezone_name).format('YYYY-MM-DD HH:mm')} | Total Entries: ${data.length}`;
    timeCell.font = { size: 10, color: { argb: '546E7A' } };
    timeCell.alignment = { horizontal: 'center' };

    // Add filter information
    currentRow++;
    worksheet.mergeCells(`A${currentRow}:I${currentRow}`);
    const filterTitleCell = worksheet.getCell(`A${currentRow}`);
    filterTitleCell.value = 'FILTER CRITERIA';
    filterTitleCell.font = { size: 12, bold: true, color: { argb: '1A237E' } };

    // Add filter details
    if (filters.startDate && filters.endDate) {
      currentRow++;
      worksheet.getCell(`A${currentRow}`).value = 'Date Range:';
      worksheet.getCell(`B${currentRow}`).value =
        `${filters.startDate} to ${filters.endDate}`;
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
    }

    if (filters.startTime && filters.endTime) {
      currentRow++;
      worksheet.getCell(`A${currentRow}`).value = 'Time Range:';
      worksheet.getCell(`B${currentRow}`).value =
        `${filters.startTime} to ${filters.endTime}`;
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
    }

    if (filters.branch) {
      currentRow++;
      worksheet.getCell(`A${currentRow}`).value = 'Branch:';
      worksheet.getCell(`B${currentRow}`).value = filters.branch.branch_name;
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
    }

    if (filters.user) {
      currentRow++;
      worksheet.getCell(`A${currentRow}`).value = 'User:';
      worksheet.getCell(`B${currentRow}`).value = filters.user.name;
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
    } else if (filters.user_labels.length > 0) {
      currentRow++;
      worksheet.getCell(`A${currentRow}`).value = 'User Labels:';
      worksheet.getCell(`B${currentRow}`).value = filters.user_labels
        .map(label => label.label_name)
        .join(', ');
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
    }

    if (filters.device) {
      currentRow++;
      worksheet.getCell(`A${currentRow}`).value = 'Device:';
      worksheet.getCell(`B${currentRow}`).value = filters.device.device_name;
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
    } else if (filters.device_labels.length > 0) {
      currentRow++;
      worksheet.getCell(`A${currentRow}`).value = 'Device Labels:';
      worksheet.getCell(`B${currentRow}`).value = filters.device_labels
        .map(label => label.label_name)
        .join(', ');
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
    }

    // Add date range explanation section
    if (filters.startDate && filters.endDate) {
      currentRow += 2;
      let startRow = currentRow;
      let endRow = currentRow;
      const cellDateRange = worksheet.getCell(`A${currentRow}`);
      cellDateRange.value = 'Date Range:';
      cellDateRange.font = { bold: true };
      cellDateRange.alignment = { horizontal: 'left', vertical: 'middle' };

      // Calculate the number of days between the start and end date
      const startDate = dayjs(filters.startDate);
      const endDate = dayjs(filters.endDate);
      const daysDiff = endDate.diff(startDate, 'day') + 1;
      const startTime = filters.startTime || '00:00';
      const endTime = filters.endTime || '23:59';

      const maxDaysToShow = 3;
      const daysToShow = Math.min(daysDiff, maxDaysToShow);

      for (let i = 0; i < daysToShow; i++) {
        if (i > 0) {
          currentRow++;
        }
        endRow = currentRow;

        const currentDate = startDate.add(i, 'day');
        const dateStr = currentDate.format('YYYY-MM-DD');
        worksheet.getCell(`B${currentRow}`).value =
          `${dateStr} ${startTime} until ${dateStr} ${endTime}`;
      }

      if (daysDiff > maxDaysToShow) {
        currentRow++;
        worksheet.getCell(`B${currentRow}`).value = '...';

        // Show last day
        currentRow++;
        endRow = currentRow;
        const dateStr = endDate.format('YYYY-MM-DD');
        worksheet.getCell(`B${currentRow}`).value =
          `${dateStr} ${startTime} until ${dateStr} ${endTime}`;
      }

      // Merge the date range cells
      worksheet.mergeCells(`A${startRow}:A${endRow}`);
    }

    // Add table headers
    const headers = [
      'UUID',
      'Date & Time',
      'Main Branch',
      'User',
      'Device',
      'Event Type',
      'Timezone',
      'Latitude',
      'Longitude',
    ];

    // Add header row with styling
    currentRow += 2;
    worksheet.addRow(2);
    const headerRow = worksheet.addRow(headers);
    headerRow.font = { bold: true, color: { argb: 'FFFFFF' } };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '1A237E' },
    };
    headerRow.alignment = { horizontal: 'center' };

    // Add data rows
    currentRow++;
    data.forEach(signInOutLog => {
      // Create dayjs object with timezone for reuse
      const originalSubmittedTime = dayjs(
        signInOutLog.event_time,
      ).tz(signInOutLog.timezone_name);

      worksheet.getCell(currentRow, 1).value = signInOutLog.uuid;
      worksheet.getCell(currentRow, 2).value =
        originalSubmittedTime.format('YYYY-MM-DD HH:mm');
      worksheet.getCell(currentRow, 3).value =
        signInOutLog.parent_branch?.branch_name || '-';
      worksheet.getCell(currentRow, 4).value = signInOutLog.user_name || '-';
      worksheet.getCell(currentRow, 5).value = signInOutLog.device_name || '-';
      worksheet.getCell(currentRow, 6).value =
        signInOutLog.user_log_type || '-';
      worksheet.getCell(currentRow, 7).value =
        signInOutLog.timezone_name || '-';
      worksheet.getCell(currentRow, 8).value = signInOutLog.latitude || '-';
      worksheet.getCell(currentRow, 9).value = signInOutLog.longitude || '-';

      // Apply alternating row colors
      if (currentRow % 2 === 0) {
        for (let i = 1; i <= headers.length; i++) {
          worksheet.getCell(currentRow, i).fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'F5F5F5' },
          };
        }
      }

      currentRow++;
    });

    // Set column widths
    worksheet.getColumn(1).width = 36; // UUID
    worksheet.getColumn(2).width = 20; // Date & Time
    worksheet.getColumn(3).width = 20; // Branch
    worksheet.getColumn(4).width = 20; // User
    worksheet.getColumn(5).width = 20; // Device
    worksheet.getColumn(6).width = 20; // Event Type
    worksheet.getColumn(7).width = 20; // Timezone
    worksheet.getColumn(8).width = 15; // Latitude
    worksheet.getColumn(9).width = 15; // Longitude

    // Add footer
    currentRow += 2;
    const footerRowIndex = currentRow;
    worksheet.mergeCells(`A${footerRowIndex}:I${footerRowIndex}`);
    const footerCell = worksheet.getCell(`A${footerRowIndex}`);
    footerCell.value = 'UniGuard Security System';
    footerCell.font = { size: 10, color: { argb: '9E9E9E' } };
    footerCell.alignment = { horizontal: 'center' };

    // Generate buffer
    const buffer = await workbook.xlsx.writeBuffer();

    return {
      buffer: buffer as Buffer,
      filename: `sign-in-out-logs-${dayjs().tz(timezone.timezone_name).format('YYYYMMDD-HHmmss')}.xlsx`,
    };
  }
}
