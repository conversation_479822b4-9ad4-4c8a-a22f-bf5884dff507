import { InjectRepository } from '@nestjs/typeorm';
import { User } from '../../database/entities/user.entity';
import { <PERSON><PERSON><PERSON>, <PERSON>, Query<PERSON><PERSON><PERSON>, Repository } from 'typeorm';
import { Injectable, UnauthorizedException } from '@nestjs/common';
import * as crypto from 'crypto';
import { Branch } from '../../database/entities/branch.entity';
import { UserBranch } from '../../database/entities/user-branch.entity';
import { AuthAccessToken } from '../../database/entities/auth-access-token.entity';
import { Module, ModuleType } from '../../database/entities/module.entity';
import { RolePermission } from '../../database/entities/role-permission.entity';
import { LogoutDto } from '../../../../modules/auth/dto/logout.dto';
import { Device } from '../../database/entities/device.entity';
import {
  LogUserDevice,
  UserLogType,
} from '../../database/entities/log-user-device.entity';
import { v4 as uuidV4 } from 'uuid';
import { CheckAuthorizationDto } from '../../../../modules/auth/dto/check-authorization.dto';
import { ConfigService } from '@nestjs/config';
import * as dayjs from 'dayjs';
import { AlertCommonService } from '../../alert/services/alert-common.service';

@Injectable()
export class AuthCommonService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Branch)
    private branchRepository: Repository<Branch>,
    @InjectRepository(UserBranch)
    private userBranchRepository: Repository<UserBranch>,
    @InjectRepository(AuthAccessToken)
    private tokenRepository: Repository<AuthAccessToken>,
    @InjectRepository(Module)
    private moduleRepository: Repository<Module>,
    @InjectRepository(RolePermission)
    private rolePermissionRepository: Repository<RolePermission>,
    @InjectRepository(Device)
    private deviceRepository: Repository<Device>,
    @InjectRepository(LogUserDevice)
    private logUserDeviceRepository: Repository<LogUserDevice>,
    private configService: ConfigService,
    private alertCommonService: AlertCommonService,
  ) {}

  async findUserByEmailAndPassword(params: {
    credentials: {
      email: string;
      password: string;
    };
    options?: {
      device?: { device_name: string; device_uid: string };
      user_agent?: string;
    };
  }) {
    const { email, password } = params.credentials;
    const user = await this.userRepository
      .createQueryBuilder('user')
      .addSelect('user.password')
      .leftJoinAndSelect('user.parent_branch', 'parent_branch')
      .leftJoinAndSelect('user.role', 'role')
      .leftJoinAndSelect('parent_branch.timezone', 'timezone')
      .where('user.email = :email', { email })
      .getOne();

    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }
    const isPasswordValid = await user.validatePassword(password);
    if (!isPasswordValid) {
      throw new UnauthorizedException('Invalid credentials');
    }
    return user;
  }

  async findUserTokenAndDelete(
    queryRunner: QueryRunner,
    user: User,
    options?: {
      device_uid?: string;
      device?: Device;
      user_agent?: string;
      geolocation?: { latitude: number; longitude: number };
    },
  ) {
    // Find token by hash using user id
    const whereCondition: any = { tokenable_id: user.id };

    if (!options?.device_uid) {
      whereCondition.device_uid = IsNull();
    } else {
      whereCondition.device_uid = Not(IsNull());
    }

    const findAccessTokens = await this.tokenRepository.find({
      where: whereCondition,
      relations: ['user'],
    });

    for (const token of findAccessTokens) {
      await queryRunner.manager.delete(AuthAccessToken, token.id);

      // Insert to Log User Device - Force Sign Out
      const logUserDevice = new LogUserDevice();
      logUserDevice.uuid = uuidV4();
      if (user.parent_branch) {
        logUserDevice.parent_branch_id = user.parent_branch_id;
      }
      logUserDevice.user_id = user.id;
      logUserDevice.role_id = user.role_id;
      logUserDevice.user_log_type = UserLogType.FORCE_SIGNOUT;
      logUserDevice.user_name = user.name;
      if (user.parent_branch?.timezone) {
        logUserDevice.timezone_id = user.parent_branch.timezone.id;
        logUserDevice.timezone_name = user.parent_branch.timezone.timezone_name;
      }
      if (options?.geolocation) {
        logUserDevice.latitude = options?.geolocation?.latitude || null;
        logUserDevice.longitude = options?.geolocation?.longitude || null;
      }

      const device =
        options?.device ||
        (options?.device_uid
          ? await this.deviceRepository.findOne({
              where: { imei: options.device_uid },
            })
          : null);
      if (device) {
        logUserDevice.device_id = device.id;
        logUserDevice.device_name = device.device_name;
      }

      logUserDevice.event_time = new Date();
      await queryRunner.manager.save(LogUserDevice, logUserDevice);
    }
  }

  async generateAndSaveHashedToken(
    queryRunner: QueryRunner,
    user: User,
    options?: {
      device_name: string;
      device_uid: string;
      user_agent?: string;
    },
  ) {
    const token = this.generateLoginToken();
    const tokenHash = crypto.createHash('sha256').update(token).digest('hex');

    // Calculate token expiration based on configuration
    const expiresIn = parseInt(
      this.configService.get<string>('TOKEN_EXPIRATION', '259200'),
    );
    const expiresAt = new Date();
    expiresAt.setSeconds(expiresAt.getSeconds() + expiresIn);

    // Determine user abilities based on access type
    let abilities: string[] = [];
    if (user.system_access) {
      abilities.push('system:*');
    } else if (user.web_access) {
      abilities.push('web:*');
    } else if (user.mobile_access) {
      abilities.push('mobile:*');
    }

    // Create and save token entity
    const accessToken = new AuthAccessToken();
    accessToken.tokenable_id = user.id;
    accessToken.type = 'access_token';
    accessToken.name = 'API Token';
    accessToken.hash = tokenHash;
    accessToken.user_agent = options?.user_agent || null;
    accessToken.device_name = options?.device_name || null;
    accessToken.device_uid = options?.device_uid || null;
    accessToken.abilities = JSON.stringify(abilities);
    accessToken.expires_at = expiresAt;
    accessToken.created_at = new Date();
    accessToken.updated_at = new Date();
    await queryRunner.manager.save(AuthAccessToken, accessToken);

    return {
      access_token: token,
      hash: tokenHash,
    };
  }

  async getOrInsertUserDevice(
    user: User,
    deviceName: string,
    deviceUid: string,
  ) {
    const existingDevice = await this.deviceRepository.findOne({
      where: {
        imei: deviceUid,
      },
    });
    if (existingDevice) {
      return existingDevice;
    } else {
      const newDevice = this.deviceRepository.create({
        device_name: deviceName,
        imei: deviceUid,
        uniguard_device_type_id: 1,
        active: true,
        parent_branch_id: user.parent_branch_id,
        created_by: user.id,
        updated_by: user.id,
      });
      return this.deviceRepository.save(newDevice);
    }
  }

  generateLoginToken() {
    return crypto.randomBytes(32).toString('hex');
  }

  async checkBranchAuthorization(
    user: User,
    branch_code: string,
  ): Promise<boolean> {
    const branch = await this.branchRepository.findOne({
      where: { branch_code: branch_code, active: true },
    });

    if (!branch) {
      return false;
    }

    // Check direct access through user_branches
    const directAccess = await this.userBranchRepository.findOne({
      where: {
        user_id: user.id,
        branch_id: branch.id,
        active: true,
      },
    });

    return !!directAccess;
  }

  async validateToken(
    token: string,
    options?: { deviceUid?: string },
  ): Promise<User> {
    const tokenHash = crypto.createHash('sha256').update(token).digest('hex');

    // Find token by hash only, then check device_uid separately if needed
    const accessToken = await this.tokenRepository.findOne({
      where: { hash: tokenHash },
      relations: ['user'],
    });

    if (!accessToken) {
      throw new UnauthorizedException('Invalid token');
    }

    if (accessToken.expires_at && new Date() > accessToken.expires_at) {
      await this.tokenRepository.delete(accessToken.id);
      throw new UnauthorizedException('Token has expired');
    }

    // Check if user has device uid
    if (options?.deviceUid) {
      if (accessToken.device_uid !== options.deviceUid) {
        throw new UnauthorizedException('Invalid device');
      }
    }

    accessToken.last_used_at = new Date();
    await this.tokenRepository.save(accessToken);

    const user = await this.userRepository.findOne({
      where: { id: accessToken.tokenable_id },
      relations: ['parent_branch', 'role', 'parent_branch.timezone'],
    });

    if (!user || !user.active) {
      throw new UnauthorizedException('User not found or inactive');
    }

    return user;
  }

  async checkRoleAuthorization(
    user: User,
    moduleCode: string,
    moduleType: ModuleType,
    authorized: 'create' | 'update' | 'view' | 'delete',
  ): Promise<boolean> {
    const module = await this.moduleRepository.findOne({
      where: {
        module_code: moduleCode,
        module_type: moduleType,
        active: true,
      },
    });

    if (!module) {
      throw new UnauthorizedException(
        `Module ${moduleCode} not found or inactive`,
      );
    }

    const rolePermission = await this.rolePermissionRepository.findOne({
      where: {
        role_id: user.role_id,
        module_id: module.id,
      },
    });

    if (rolePermission) {
      switch (authorized) {
        case 'create':
          return rolePermission.allow_create;
        case 'update':
          return rolePermission.allow_update;
        case 'view':
          return rolePermission.allow_view;
        case 'delete':
          return rolePermission.allow_delete;
      }
    }

    return false;
  }

  public async checkAuthorization(
    checkAuthorizationDto: CheckAuthorizationDto,
    user: User,
  ) {
    // Load user dengan relasi yang dibutuhkan
    const userWithRelations = await this.userRepository.findOne({
      where: { id: user.id },
      relations: ['parent_branch'],
    });

    if (!userWithRelations) {
      throw new UnauthorizedException('User not found');
    }

    const result = {
      user: {
        id: userWithRelations.id,
        name: userWithRelations.name,
        email: userWithRelations.email,
        parent_branch: userWithRelations.parent_branch,
      },
      isValid: true,
      hasWebAccess: userWithRelations.web_access,
      hasMobileAccess: userWithRelations.mobile_access,
      hasSystemAccess: userWithRelations.system_access,
      hasBranchAccess: true,
      hasModulePermission: true,
    };

    if (checkAuthorizationDto.branch_code) {
      result.hasBranchAccess = await this.checkBranchAuthorization(
        userWithRelations,
        checkAuthorizationDto.branch_code,
      );
    }

    if (checkAuthorizationDto.module) {
      result.hasModulePermission = await this.checkRoleAuthorization(
        userWithRelations,
        checkAuthorizationDto.module.code,
        checkAuthorizationDto.module.type,
        checkAuthorizationDto.module.authorization,
      );
    }

    return result;
  }

  async logout(
    token: string,
    data: LogoutDto,
    user: User,
    options?: {
      device_uid?: string;
    },
  ) {
    const { latitude, longitude } = data;
    const tokenHash = crypto.createHash('sha256').update(token).digest('hex');

    const accessToken = await this.tokenRepository.findOne({
      where: { hash: tokenHash },
      relations: ['user'],
    });

    if (!accessToken) {
      throw new UnauthorizedException('Invalid token');
    }

    await this.tokenRepository.delete(accessToken.id);

    let device: Device | null = null;
    // Find Device
    if (options?.device_uid) {
      device = await this.deviceRepository.findOne({
        where: {
          imei: options.device_uid,
        },
      });
    }

    const logUserDevice = new LogUserDevice();
    logUserDevice.uuid = uuidV4();
    if (user.parent_branch) {
      logUserDevice.parent_branch_id = user.parent_branch_id;
    }
    logUserDevice.user_id = user.id;
    logUserDevice.role_id = user.role_id;
    logUserDevice.user_log_type = UserLogType.SIGNOUT;
    logUserDevice.user_name = user.name;
    if (user.parent_branch?.timezone) {
      logUserDevice.timezone_id = user.parent_branch.timezone.id;
      logUserDevice.timezone_name = user.parent_branch.timezone.timezone_name;
    }
    logUserDevice.latitude = latitude || null;
    logUserDevice.longitude = longitude || null;
    if (device) {
      logUserDevice.device_id = device.id as any;
      logUserDevice.device_name = device.device_name;
    }
    logUserDevice.event_time = new Date();
    await this.logUserDeviceRepository.save(logUserDevice);

    this.sendAlert(logUserDevice).then().catch();
  }

  public sendAlert(logUserDevice: LogUserDevice) {
    return this.alertCommonService.processAlert({
      alertEventId: 7,
      logSignInOut: logUserDevice,
      userId: logUserDevice.user_id,
      roleId: logUserDevice.role_id,
      submittedDateTime: dayjs(logUserDevice.event_time).toISOString(),
      deviceId: logUserDevice.device_id,
      parentBranchId: logUserDevice.parent_branch_id,
    });
  }
}
