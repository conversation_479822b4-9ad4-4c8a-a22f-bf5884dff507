import {
  <PERSON><PERSON><PERSON>,
  CreateDate<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { Branch } from './branch.entity';
import { User } from './user.entity';
import { Role } from './role.entity';
import { AlertEvent } from './alert-event.entity';

@Entity({ name: 'log_alerts', schema: 'public' })
export class LogAlert {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id: number;

  @Column({ type: 'uuid', nullable: true })
  uuid: string;

  @Column({ name: 'alert_event_id', type: 'bigint', nullable: true })
  alert_event_id: number;

  @ManyToOne(() => AlertEvent)
  @JoinColumn({ name: 'alert_event_id' })
  alert_event: AlertEvent;

  @Column({ name: 'alert_event_name', nullable: true })
  alert_event_name: string;

  @Column({ name: 'log_id', type: 'bigint', nullable: true })
  log_id: number;

  @Column({ name: 'log_uuid', type: 'uuid', nullable: true })
  log_uuid: string;

  @Column({ name: 'reference_name', nullable: true })
  reference_name: string;

  @Column({ name: 'parent_branch_id', type: 'bigint', nullable: true })
  parent_branch_id: number;

  @ManyToOne(() => Branch)
  @JoinColumn({ name: 'parent_branch_id' })
  parent_branch: Branch;

  @Column({ name: 'branch_id', type: 'bigint', nullable: true })
  branch_id: number;

  @ManyToOne(() => Branch)
  @JoinColumn({ name: 'branch_id' })
  branch: Branch;

  @Column({ name: 'user_id', type: 'bigint', nullable: true })
  user_id: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({ name: 'role_id', type: 'bigint', nullable: true })
  role_id: number;

  @ManyToOne(() => Role)
  @JoinColumn({ name: 'role_id' })
  role: Role;

  @Column({ name: 'payload_data', type: 'jsonb', nullable: true })
  payload_data: any;

  @Column({ name: 'deleted_on_dashboard', type: 'boolean', nullable: true })
  deleted_on_dashboard: boolean;

  @Column({ name: 'user_name', type: 'varchar', length: 100, nullable: true })
  user_name: string;

  @Column({ name: 'branch_name', type: 'varchar', length: 100, nullable: true })
  branch_name: string;

  @Column({ name: 'device_id', type: 'bigint', nullable: true })
  device_id: number;

  @Column({ name: 'device_name', type: 'varchar', length: 100, nullable: true })
  device_name: string;

  @Column({ name: 'latitude', type: 'double precision', nullable: true })
  latitude: number;

  @Column({ name: 'longitude', type: 'double precision', nullable: true })
  longitude: number;

  @Column({ name: 'timezone_id', type: 'varchar', length: 50, nullable: true })
  timezone_id: string;

  @Column({ name: 'timezone_name', type: 'varchar', length: 100, nullable: true })
  timezone_name: string;

  @Column({
    name: 'original_submitted_time',
    type: 'timestamp with time zone',
    nullable: true,
  })
  original_submitted_time: Date;

  @CreateDateColumn({
    name: 'event_time',
    type: 'timestamp with time zone',
    default: () => 'CURRENT_TIMESTAMP',
    nullable: false,
  })
  event_time: Date;
}
