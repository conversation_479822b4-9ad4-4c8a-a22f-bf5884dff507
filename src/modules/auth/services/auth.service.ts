import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { User } from '../../../common/modules/database/entities/user.entity';
import { LogUserDevice, UserLogType } from '../../../common/modules/database/entities/log-user-device.entity';
import { v4 as uuidV4 } from 'uuid';
import { LoginDto } from '../dto/login.dto';
import { Device } from '../../../common/modules/database/entities/device.entity';
import { AuthCommonService as CommonAuthService } from '../../../common/modules/auth/services/auth-common.service';
import { LogoutDto } from '../dto/logout.dto';
import { CheckAuthorizationDto } from '../dto/check-authorization.dto';
import { ModuleType } from '../../../common/modules/database/entities/module.entity';
import { AlertCommonService } from '../../../common/modules/alert/services/alert-common.service';
import * as dayjs from 'dayjs';

/**
 * Service responsible for handling authentication and authorization operations.
 * Manages user login, token generation, validation, and permission checks.
 */
@Injectable()
export class AuthService {
  constructor(
    @InjectRepository(Device)
    private deviceRepository: Repository<Device>,
    private dataSource: DataSource,
    private commonAuthService: CommonAuthService,
    private alertCommonService: AlertCommonService,
  ) {}

  /**
   * Authenticates a user using email and password, then generates an access token.
   * @returns Object containing access token and user information
   * @throws UnauthorizedException if credentials are invalid
   * @param data
   * @param options
   */
  async loginByEmailAndPassword(
    data: LoginDto,
    options?: {
      device?: { device_name: string; device_uid: string };
      user_agent?: string;
    },
  ) {
    const { email, password, latitude, longitude } = data;
    const user = await this.commonAuthService.findUserByEmailAndPassword({
      credentials: { email, password },
      options: {
        device: options?.device,
        user_agent: options?.user_agent,
      },
    });

    let device: Device | null = null;
    // Find Device
    if (options?.device?.device_uid) {
      device = await this.deviceRepository.findOne({
        where: {
          imei: options.device.device_uid,
        },
      });
    }

    // Start transaction
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Find and delete active tokens for this user
      await this.commonAuthService.findUserTokenAndDelete(queryRunner, user, {
        device_uid: options?.device?.device_uid || '',
        user_agent: options?.user_agent || '',
        device: device || undefined,
        geolocation:
          latitude && longitude
            ? {
                latitude: latitude,
                longitude: longitude,
              }
            : undefined,
      });

      const logUserDevice = new LogUserDevice();
      if (!user.system_access) {
        // Insert to Log User Device - Login
        logUserDevice.uuid = uuidV4();
        logUserDevice.parent_branch_id = user.parent_branch_id;
        logUserDevice.user_id = user.id;
        logUserDevice.role_id = user.role_id;
        logUserDevice.user_log_type = UserLogType.SIGNIN;
        logUserDevice.user_name = user.name;
        logUserDevice.timezone_id = user.parent_branch.timezone.id;
        logUserDevice.timezone_name = user.parent_branch.timezone.timezone_name;
        logUserDevice.latitude = latitude || null;
        logUserDevice.longitude = longitude || null;
        if (device) {
          logUserDevice.device_id = device.id as any;
          logUserDevice.device_name = device.device_name;
        }
        logUserDevice.event_time = new Date();

        if (options?.device) {
          const device = await this.commonAuthService.getOrInsertUserDevice(
            user,
            options.device.device_name,
            options.device.device_uid,
          );
          logUserDevice.device_id = device.id as any;
          logUserDevice.device_name = device.device_name;
        }
        await queryRunner.manager.save(LogUserDevice, logUserDevice);
      }

      const token = await this.commonAuthService.generateAndSaveHashedToken(
        queryRunner,
        user,
        {
          device_name: options?.device?.device_name || '',
          device_uid: options?.device?.device_uid || '',
          user_agent: options?.user_agent || '',
        },
      );

      // Commit transaction
      await queryRunner.commitTransaction();

      if (!user.system_access) {
        this.sendAlert(logUserDevice).then().catch();
      }

      return {
        access_token: token.access_token,
        token_type: 'Bearer',
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          web_access: user.web_access,
          mobile_access: user.mobile_access,
          system_access: user.system_access,
          parent_branch: user.parent_branch,
          role: user.role,
        },
      };
    } catch (error) {
      // Rollback transaction on error
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      // Release query runner
      await queryRunner.release();
    }
  }

  async logout(
    token: string,
    data: LogoutDto,
    user: User,
    options?: {
      device_uid?: string;
    },
  ) {
    return await this.commonAuthService.logout(token, data, user, options);
  }

  async checkBranchAuthorization(
    user: User,
    branch_code: string,
  ): Promise<boolean> {
    return await this.commonAuthService.checkBranchAuthorization(
      user,
      branch_code,
    );
  }

  async checkAuthorization(
    checkAuthorizationDto: CheckAuthorizationDto,
    user: User,
  ) {
    return await this.commonAuthService.checkAuthorization(
      checkAuthorizationDto,
      user,
    );
  }

  async checkRoleAuthorization(
    user: User,
    moduleCode: string,
    moduleType: ModuleType,
    authorized: 'create' | 'update' | 'view' | 'delete',
  ) {
    return await this.commonAuthService.checkRoleAuthorization(
      user,
      moduleCode,
      moduleType,
      authorized,
    );
  }

  public async sendAlert(logUserDevice: LogUserDevice) {
    return await this.alertCommonService.processAlert({
      alertEventId: 6,
      logSignInOut: logUserDevice,
      userId: logUserDevice.user_id,
      roleId: logUserDevice.role_id,
      submittedDateTime: dayjs(logUserDevice.event_time).toISOString(),
      deviceId: logUserDevice.device_id,
      parentBranchId: logUserDevice.parent_branch_id,
    });
  }
}
