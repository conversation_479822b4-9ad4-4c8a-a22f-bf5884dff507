import { Global, Module } from '@nestjs/common';
import { AuthController } from './controllers/auth.controller';
import { AuthService } from './services/auth.service';
import { AlertCommonModule } from '../../common/modules/alert/alert-common.module';

@Global()
@Module({
  imports: [AlertCommonModule],
  controllers: [AuthController],
  providers: [AuthService],
  exports: [],
})
export class AuthModule {}
